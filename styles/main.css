/* Main CSS file for Verbatims */

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  color: rgb(var(--color-gray-900));
  background-color: rgb(var(--color-gray-50));
}

/* Dark mode support */
.dark body {
  color: rgb(var(--color-gray-100));
  background-color: rgb(var(--color-gray-900));
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgb(var(--color-gray-100));
}

::-webkit-scrollbar-thumb {
  background: rgb(var(--color-gray-400));
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--color-gray-500));
}

.dark ::-webkit-scrollbar-track {
  background: rgb(var(--color-gray-800));
}

.dark ::-webkit-scrollbar-thumb {
  background: rgb(var(--color-gray-600));
}

/* Quote card animations */
.quote-card {
  transition: all 0.2s ease-in-out;
}

.quote-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Loading animations */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.dark .loading-skeleton {
  background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
  background-size: 200% 100%;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Fade in animation */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Grid layout for quotes */
.quotes-grid-enhanced {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  padding: 0;
}

@media (min-width: 640px) {
  .quotes-grid-enhanced {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
    gap: 2rem;
  }
}

@media (min-width: 1024px) {
  .quotes-grid-enhanced {
    grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
    gap: 2.5rem;
  }
}

@media (min-width: 1280px) {
  .quotes-grid-enhanced {
    grid-template-columns: repeat(auto-fill, minmax(450px, 1fr));
    gap: 3rem;
  }
}

/* Quote skeleton for loading state */
.quote-skeleton {
  border: 1px dashed rgb(var(--color-gray-200) / 0.8);
  border-radius: 0.75rem;
  padding: 1.5rem;
  background: rgb(var(--color-gray-50) / 0.5);
}

.dark .quote-skeleton {
  border-color: rgb(var(--color-gray-700) / 0.6);
  background: rgb(var(--color-gray-800) / 0.3);
}

/* Legacy grid support */
.quotes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
  padding: 1rem;
}

@media (min-width: 768px) {
  .quotes-grid {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 2rem;
    padding: 2rem;
  }
}

/* Mobile bottom navigation spacing */
@media (max-width: 767px) {
  body {
    padding-bottom: 80px; /* Height of bottom navigation */
  }

  /* Ensure mobile pages have proper spacing */
  .mobile-layout main,
  .mobile-search-page,
  .mobile-diary-page {
    padding-bottom: 2rem;
    margin-bottom: 80px;
  }
}

/* Enhanced typography for quotes */
.quote-text {
  font-size: 1.125rem;
  line-height: 1.75;
  font-weight: 500;
  letter-spacing: -0.01em;
  color: rgb(var(--color-gray-900));
}

.dark .quote-text {
  color: rgb(var(--color-gray-100));
}

@media (min-width: 640px) {
  .quote-text {
    font-size: 1.25rem;
    line-height: 1.8;
  }
}

@media (min-width: 1024px) {
  .quote-text {
    font-size: 1.375rem;
    line-height: 1.85;
  }
}

/* Quote author typography */
.quote-author {
  font-size: 0.95rem;
  font-weight: 600;
  letter-spacing: -0.005em;
  color: rgb(var(--color-gray-700));
}

.dark .quote-author {
  color: rgb(var(--color-gray-300));
}

@media (min-width: 640px) {
  .quote-author {
    font-size: 1rem;
  }
}

/* Quote reference typography */
.quote-reference {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgb(var(--color-gray-600));
}

.dark .quote-reference {
  color: rgb(var(--color-gray-400));
}

/* Enhanced readability */
.quote-content {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Tag styles */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.tag:hover {
  transform: scale(1.05);
}

/* Button hover effects */
.btn-hover {
  transition: all 0.2s ease-in-out;
}

.btn-hover:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Focus styles for accessibility */
.focus-ring:focus {
  outline: 2px solid rgb(var(--color-primary-500));
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .quote-card {
    break-inside: avoid;
    margin-bottom: 1rem;
  }
}

/* Utility classes */
.text-balance {
  text-wrap: balance;
}

.text-pretty {
  text-wrap: pretty;
}

/* Custom selection colors */
::selection {
  background-color: black;
  color: white;
}

.dark ::selection {
  background-color: white;
  color: black;
}
