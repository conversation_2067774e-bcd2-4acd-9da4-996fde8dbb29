<template>
  <div class="min-h-screen bg-gray-50 dark:bg-[#0C0A09] mobile-layout">
    <!-- Mobile Header (simplified) -->
    <header 
      class="fixed top-0 left-0 right-0 z-40 bg-white dark:bg-[#0C0A09] border-b border-dashed border-gray-200 dark:border-gray-700 px-4 py-3"
    >
      <div class="flex items-center justify-between">
        <!-- Logo/Brand -->
        <UButton btn="~" @click="handleLogoClick" class="cursor-pointer hover:scale-105 active:scale-95 transition-transform">
          <AppIcon />
        </UButton>

        <!-- Right side actions -->
        <div class="flex items-center space-x-2">
          <!-- Add Quote <PERSON><PERSON> (only show on certain pages) -->
          <UButton
            v-if="shouldShowAddQuote"
            icon
            btn="ghost-gray"
            label="i-ph-plus-bold"
            @click="showAddQuote = true"
            title="Add Quote"
            size="sm"
          />

          <!-- User Menu or Login -->
          <UserMenu v-if="user" :user="user" />
          <UButton v-else btn="outline-dark dark:outline-white" to="/login" size="sm">
            Log in
          </UButton>
        </div>
      </div>
    </header>
    
    <!-- Main Content with proper spacing for fixed header and bottom nav -->
    <main class="pt-16 pb-20 min-h-screen">
      <slot />
    </main>

    <!-- Global Modals -->
    <AddQuoteDialog 
      v-model="showAddQuote"
      @quote-added="handleQuoteAdded"
    />
  </div>
</template>

<script setup lang="ts">
const route = useRoute()
const { user } = useUserSession()
const showAddQuote = ref(false)

// Determine if we should show the add quote button
const shouldShowAddQuote = computed(() => {
  // Show on home, search, and diary pages
  return ['/', '/search', '/diary'].includes(route.path) || route.path.startsWith('/diary')
})

const handleLogoClick = (event: MouseEvent) => {
  if (route.path !== '/') {
    navigateTo('/')
    return
  }

  // If we're on the home page, prevent navigation and scroll to top instead
  event.preventDefault()
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

const handleQuoteAdded = () => {
  // Handle quote added - could refresh data or show toast
  showAddQuote.value = false
}

// Set page meta to use mobile layout on mobile devices
definePageMeta({
  layout: false // We'll handle layout switching in the page components
})
</script>

<style scoped>
/* Mobile-specific styles */
.mobile-layout {
  /* Ensure proper spacing for fixed elements */
  --header-height: 64px;
  --bottom-nav-height: 80px;
}

/* Override any desktop-specific styles */
@media (max-width: 767px) {
  .mobile-layout main {
    /* Ensure content doesn't get hidden behind fixed elements */
    min-height: calc(100vh - var(--header-height) - var(--bottom-nav-height));
  }
}

/* Hide desktop header when using mobile layout */
.mobile-layout :deep(.desktop-header) {
  display: none;
}
</style>
