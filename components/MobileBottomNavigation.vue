<template>
  <nav 
    class="fixed bottom-0 left-0 right-0 z-50 bg-white dark:bg-[#0C0A09] border-t border-dashed border-gray-200 dark:border-gray-700 px-4 py-2 safe-area-pb"
    :class="{ 'hidden': !isMobile }"
  >
    <div class="flex justify-around items-center max-w-md mx-auto">
      <!-- Home Tab -->
      <NuxtLink
        to="/"
        class="flex flex-col items-center justify-center p-3 rounded-lg transition-all duration-200 min-w-16"
        :class="[
          isActiveRoute('/') 
            ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20' 
            : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
        ]"
        @click="handleNavigation('/')"
      >
        <UIcon 
          name="i-ph-house-bold" 
          class="w-6 h-6 mb-1"
          :class="{ 'text-primary-600 dark:text-primary-400': isActiveRoute('/') }"
        />
        <span class="text-xs font-medium">Home</span>
      </NuxtLink>

      <!-- Search Tab -->
      <NuxtLink
        to="/search"
        class="flex flex-col items-center justify-center p-3 rounded-lg transition-all duration-200 min-w-16"
        :class="[
          isActiveRoute('/search') 
            ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20' 
            : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
        ]"
        @click="handleNavigation('/search')"
      >
        <UIcon 
          name="i-ph-magnifying-glass-bold" 
          class="w-6 h-6 mb-1"
          :class="{ 'text-primary-600 dark:text-primary-400': isActiveRoute('/search') }"
        />
        <span class="text-xs font-medium">Search</span>
      </NuxtLink>

      <!-- Diary Tab -->
      <NuxtLink
        to="/diary"
        class="flex flex-col items-center justify-center p-3 rounded-lg transition-all duration-200 min-w-16"
        :class="[
          isActiveRoute('/diary') 
            ? 'text-primary-600 dark:text-primary-400 bg-primary-50 dark:bg-primary-900/20' 
            : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
        ]"
        @click="handleNavigation('/diary')"
      >
        <UIcon 
          name="i-ph-book-open-bold" 
          class="w-6 h-6 mb-1"
          :class="{ 'text-primary-600 dark:text-primary-400': isActiveRoute('/diary') }"
        />
        <span class="text-xs font-medium">Diary</span>
      </NuxtLink>
    </div>
  </nav>
</template>

<script setup lang="ts">
const route = useRoute()

// Use the mobile detection composable
const { isMobile } = useMobileDetection()

// Check if current route matches the tab
const isActiveRoute = (path: string): boolean => {
  if (path === '/') {
    return route.path === '/'
  }
  return route.path.startsWith(path)
}

// Handle navigation with haptic feedback on mobile
const handleNavigation = (path: string) => {
  // Add haptic feedback if available
  if (process.client && 'vibrate' in navigator) {
    navigator.vibrate(50)
  }

  // Navigate to the path
  if (route.path !== path) {
    navigateTo(path)
  }
}
</script>

<style scoped>
/* Safe area support for devices with notches */
.safe-area-pb {
  padding-bottom: max(0.5rem, env(safe-area-inset-bottom));
}

/* Ensure navigation doesn't interfere with content */
@media (max-width: 767px) {
  body {
    padding-bottom: 80px; /* Height of bottom navigation */
  }
}
</style>
