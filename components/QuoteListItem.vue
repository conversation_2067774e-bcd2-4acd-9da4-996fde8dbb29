<template>
  <div
    class="quote-list-item group border-b border-dashed border-gray-200 dark:border-gray-400 py-6 px-4 cursor-pointer transition-all duration-300 hover:bg-gray-50 dark:hover:bg-gray-800/50"
    @click="navigateToQuote"
    @mouseenter="isHovered = true"
    @mouseleave="isHovered = false"
  >
    <!-- Author and Reference Info (Top) -->
    <div 
      class="flex items-center justify-between mb-3 text-sm font-600 text-gray-600 dark:text-gray-400 transition-opacity duration-300"
      :class="{ 'opacity-100': isHovered, 'opacity-70': !isHovered }"
    >
      <!-- Author Name -->
      <span v-if="quote.author" class="truncate">
        {{ quote.author.name }}
      </span>
      <span v-else class="text-gray-400 dark:text-gray-500 italic">
        Unknown Author
      </span>

      <!-- Reference Info -->
      <div v-if="quote.reference" class="flex items-center space-x-2 text-xs">
        <span class="truncate max-w-32">
          {{ quote.reference.name }}
        </span>
        <UBadge
          v-if="quote.reference.primary_type"
          :color="getReferenceTypeColor(quote.reference.primary_type)"
          variant="subtle"
          size="xs"
        >
          {{ quote.reference.primary_type }}
        </UBadge>
      </div>
    </div>

    <!-- Quote Content (Main) -->
    <div class="mb-3">
      <blockquote
        class="font-serif text-gray-800 dark:text-gray-200 leading-relaxed"
        :class="{
          'text-sm': (quote.name || '').length > 200,
          'text-base': (quote.name || '').length <= 200 && (quote.name || '').length > 100,
          'text-lg': (quote.name || '').length <= 100
        }"
      >
        {{ quote.name }}
      </blockquote>
    </div>

    <!-- Bottom Row: Tags and Actions -->
    <div class="flex items-center justify-between">
      <!-- Tags (if any) -->
      <div class="flex items-center space-x-2">
        <UBadge
          v-if="quote.is_featured"
          color="yellow"
          variant="subtle"
          size="xs"
        >
          Featured
        </UBadge>
        
        <!-- Language Badge -->
        <UBadge
          v-if="quote.language && quote.language !== 'en'"
          color="blue"
          variant="subtle"
          size="xs"
        >
          {{ quote.language.toUpperCase() }}
        </UBadge>
      </div>

      <!-- Action Buttons (show on hover/touch) -->
      <div 
        class="flex items-center space-x-2 transition-opacity duration-300"
        :class="{ 'opacity-100': isHovered, 'opacity-0': !isHovered }"
      >
        <!-- Like Button -->
        <UButton
          icon
          btn="ghost-gray"
          label="i-ph-heart"
          size="xs"
          @click.stop="handleLike"
          class="hover:text-red-500"
        />
        
        <!-- Share Button -->
        <UButton
          icon
          btn="ghost-gray"
          label="i-ph-share"
          size="xs"
          @click.stop="handleShare"
          class="hover:text-blue-500"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  quote: {
    type: Object,
    required: true
  }
})

const isHovered = ref(false)

const navigateToQuote = () => {
  navigateTo(`/quote/${props.quote.id}`)
}

const getReferenceTypeColor = (type) => {
  const colors = {
    'book': 'green',
    'film': 'blue',
    'tv_series': 'purple',
    'video_game': 'orange',
    'music': 'pink',
    'podcast': 'indigo',
    'documentary': 'teal',
    'speech': 'red'
  }
  return colors[type] || 'gray'
}

const handleLike = () => {
  // TODO: Implement like functionality
  console.log('Like quote:', props.quote.id)
}

const handleShare = () => {
  // TODO: Implement share functionality
  if (navigator.share) {
    navigator.share({
      title: 'Quote from Verbatims',
      text: props.quote.name,
      url: `${window.location.origin}/quote/${props.quote.id}`
    })
  } else {
    // Fallback: copy to clipboard
    navigator.clipboard.writeText(`"${props.quote.name}" - ${props.quote.author?.name || 'Unknown'}`)
  }
}
</script>

<style scoped>
/* Mobile-specific optimizations */
@media (max-width: 767px) {
  .quote-list-item {
    /* Always show action buttons on mobile for better accessibility */
    .opacity-0 {
      opacity: 0.6;
    }
  }
}
</style>
